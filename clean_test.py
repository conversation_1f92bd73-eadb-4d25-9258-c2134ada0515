#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
import sys

def test_shortcut():
    """测试快捷键功能"""
    print("Ctrl+H 快捷键被触发！")
    
    # 尝试显示/隐藏控制台
    try:
        if sys.platform == "win32":
            import ctypes
            kernel32 = ctypes.windll.kernel32
            user32 = ctypes.windll.user32
            
            # 获取控制台窗口句柄
            console_window = kernel32.GetConsoleWindow()
            
            if console_window:
                # 检查当前窗口状态
                import ctypes.wintypes
                placement = ctypes.wintypes.WINDOWPLACEMENT()
                placement.length = ctypes.sizeof(placement)
                user32.GetWindowPlacement(console_window, ctypes.byref(placement))
                
                if placement.showCmd == 1:  # SW_SHOWNORMAL
                    # 当前显示，隐藏它
                    user32.ShowWindow(console_window, 0)  # SW_HIDE
                    print("控制台已隐藏")
                else:
                    # 当前隐藏，显示它
                    user32.ShowWindow(console_window, 1)  # SW_SHOWNORMAL
                    user32.SetForegroundWindow(console_window)
                    print("控制台已显示")
            else:
                # 没有控制台，创建一个
                kernel32.AllocConsole()
                console_window = kernel32.GetConsoleWindow()
                if console_window:
                    user32.ShowWindow(console_window, 1)
                    print("新控制台已创建并显示")
        else:
            print("非Windows系统，无法操作控制台")
            
    except Exception as e:
        print(f"操作控制台失败: {e}")

def main():
    """主函数"""
    root = tk.Tk()
    root.title("快捷键测试 - Ctrl+H")
    root.geometry("500x300")
    root.configure(bg='lightblue')
    
    # 标题
    title_label = tk.Label(root, text="快捷键测试程序", 
                          font=("Arial", 16, "bold"), 
                          bg='lightblue', fg='darkblue')
    title_label.pack(pady=30)
    
    # 说明
    info_label = tk.Label(root, text="按 Ctrl+H 切换控制台显示/隐藏", 
                         font=("Arial", 12), 
                         bg='lightblue', fg='black')
    info_label.pack(pady=20)
    
    # 手动测试按钮
    test_button = tk.Button(root, text="手动测试 (等同于Ctrl+H)", 
                           command=test_shortcut,
                           font=("Arial", 12), 
                           bg='white', fg='black',
                           width=25, height=2)
    test_button.pack(pady=20)
    
    # 退出按钮
    exit_button = tk.Button(root, text="退出", 
                           command=root.quit,
                           font=("Arial", 12), 
                           bg='red', fg='white',
                           width=10, height=1)
    exit_button.pack(pady=20)
    
    # 绑定快捷键事件
    def on_ctrl_h(event):
        print(f"快捷键事件触发: {event}")
        print(f"按键: {event.keysym}, 状态: {event.state}")
        test_shortcut()
        return "break"
    
    # 绑定多种快捷键格式
    root.bind('<Control-h>', on_ctrl_h)
    root.bind('<Control-H>', on_ctrl_h)
    root.bind('<Control-Key-h>', on_ctrl_h)
    root.bind('<Control-Key-H>', on_ctrl_h)
    
    # 绑定所有按键事件用于调试
    def on_any_key(event):
        # 只显示Ctrl组合键
        if event.state & 0x4:  # Ctrl键被按下
            print(f"Ctrl组合键: Ctrl+{event.keysym} (keycode: {event.keycode})")
    
    root.bind('<KeyPress>', on_any_key)
    
    # 确保窗口获得焦点
    root.focus_set()
    root.lift()
    root.attributes('-topmost', True)
    root.after(100, lambda: root.attributes('-topmost', False))
    
    print("=" * 50)
    print("快捷键测试程序已启动")
    print("=" * 50)
    print("1. 点击窗口确保它有焦点")
    print("2. 按 Ctrl+H 测试快捷键")
    print("3. 或点击按钮手动测试")
    print("=" * 50)
    
    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
