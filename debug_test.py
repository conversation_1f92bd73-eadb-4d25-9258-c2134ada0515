import tkinter as tk
from tkinter import messagebox
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# 调试模式全局变量
DEBUG_MODE = False
DEBUG_PASSWORD = "niumaliqi"

def debug_log(message, level="INFO"):
    """调试模式专用日志函数"""
    if DEBUG_MODE:
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        debug_message = f"[{timestamp}] [DEBUG-{level}] {message}"
        logging.info(debug_message)
        print(debug_message)

def toggle_debug_mode():
    """切换调试模式"""
    global DEBUG_MODE
    print("toggle_debug_mode 函数被调用")
    
    # 获取主窗口引用
    try:
        root_window = None
        for widget in tk._default_root.winfo_children():
            if isinstance(widget, tk.Tk):
                root_window = widget
                break
        if root_window is None:
            root_window = tk._default_root
    except:
        root_window = None

    # 创建密码输入对话框
    password_dialog = tk.Toplevel(root_window)
    password_dialog.title("调试模式验证")
    password_dialog.geometry("300x150")
    password_dialog.configure(bg='#f0f0f0')
    password_dialog.resizable(False, False)

    # 居中显示
    if root_window:
        password_dialog.transient(root_window)
    password_dialog.grab_set()
    
    # 将窗口置于屏幕中央
    password_dialog.update_idletasks()
    x = (password_dialog.winfo_screenwidth() // 2) - 150
    y = (password_dialog.winfo_screenheight() // 2) - 75
    password_dialog.geometry(f"300x150+{x}+{y}")

    # 密码输入框
    tk.Label(password_dialog, text="请输入调试模式密码:",
             font=("微软雅黑", 10), bg='#f0f0f0').pack(pady=20)

    password_var = tk.StringVar()
    password_entry = tk.Entry(password_dialog, textvariable=password_var,
                             show="*", font=("微软雅黑", 10), width=20)
    password_entry.pack(pady=10)
    password_entry.focus()

    def verify_password():
        if password_var.get() == DEBUG_PASSWORD:
            global DEBUG_MODE
            DEBUG_MODE = not DEBUG_MODE
            password_dialog.destroy()

            # 显示调试模式状态
            status = "已开启" if DEBUG_MODE else "已关闭"
            messagebox.showinfo("调试模式", f"调试模式{status}")

            if DEBUG_MODE:
                debug_log("调试模式已启用")
            else:
                logging.info("调试模式已关闭")
        else:
            messagebox.showerror("错误", "密码错误！")
            password_entry.delete(0, tk.END)
            password_entry.focus()

    def cancel():
        password_dialog.destroy()

    # 按钮
    button_frame = tk.Frame(password_dialog, bg='#f0f0f0')
    button_frame.pack(pady=10)

    tk.Button(button_frame, text="确定", command=verify_password,
              font=("微软雅黑", 9), width=8).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="取消", command=cancel,
              font=("微软雅黑", 9), width=8).pack(side=tk.LEFT, padx=5)

    # 绑定回车键
    password_entry.bind('<Return>', lambda e: verify_password())
    password_dialog.bind('<Escape>', lambda e: cancel())

def create_test_window():
    """创建测试窗口"""
    root = tk.Tk()
    root.title("调试模式快捷键测试")
    root.geometry("400x300")
    root.configure(bg='lightgray')
    
    # 添加一些控件
    tk.Label(root, text="调试模式快捷键测试", font=("微软雅黑", 16), bg='lightgray').pack(pady=20)
    tk.Label(root, text="按 Ctrl+H 触发调试模式", font=("微软雅黑", 12), bg='lightgray').pack(pady=10)
    
    # 添加一个按钮用于手动测试
    tk.Button(root, text="手动触发调试模式", command=toggle_debug_mode, 
              font=("微软雅黑", 12), width=20).pack(pady=20)
    
    # 添加一个文本框用于测试焦点
    entry = tk.Entry(root, font=("微软雅黑", 12), width=30)
    entry.pack(pady=10)
    entry.insert(0, "在这里输入文字测试焦点")
    
    # 绑定调试模式快捷键
    def on_debug_shortcut(event):
        try:
            print(f"调试快捷键被触发: {event}")
            print(f"事件类型: {event.type}")
            print(f"按键代码: {event.keycode}")
            print(f"按键符号: {event.keysym}")
            print(f"状态: {event.state}")
            logging.info(f"调试快捷键被触发: Ctrl+H")
            toggle_debug_mode()
            return "break"
        except Exception as e:
            print(f"调试快捷键处理出错: {e}")
            logging.error(f"调试快捷键处理出错: {e}")

    # 测试多种绑定方式
    print("开始绑定快捷键...")
    
    # 方式1: 标准绑定
    root.bind('<Control-h>', on_debug_shortcut)
    root.bind('<Control-H>', on_debug_shortcut)
    print("绑定方式1: <Control-h> 和 <Control-H>")
    
    # 方式2: Key绑定
    root.bind('<Control-Key-h>', on_debug_shortcut)
    root.bind('<Control-Key-H>', on_debug_shortcut)
    print("绑定方式2: <Control-Key-h> 和 <Control-Key-H>")
    
    # 方式3: 使用键码
    root.bind('<Control-KeyPress-h>', on_debug_shortcut)
    root.bind('<Control-KeyPress-H>', on_debug_shortcut)
    print("绑定方式3: <Control-KeyPress-h> 和 <Control-KeyPress-H>")
    
    # 确保窗口可以接收键盘事件
    root.focus_set()
    
    # 绑定到所有子控件
    def bind_to_all_children(widget):
        try:
            widget.bind('<Control-h>', on_debug_shortcut)
            widget.bind('<Control-H>', on_debug_shortcut)
            widget.bind('<Control-Key-h>', on_debug_shortcut)
            widget.bind('<Control-Key-H>', on_debug_shortcut)
            for child in widget.winfo_children():
                bind_to_all_children(child)
        except:
            pass
    
    bind_to_all_children(root)
    print("快捷键绑定完成")
    
    # 添加键盘事件监听用于调试
    def on_key_press(event):
        if event.state & 0x4:  # Ctrl键被按下
            print(f"检测到Ctrl组合键: Ctrl+{event.keysym}")
    
    root.bind('<KeyPress>', on_key_press)
    
    print("测试窗口创建完成，请尝试按 Ctrl+H")
    root.mainloop()

if __name__ == "__main__":
    create_test_window()
