#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件化系统核心框架
"""

import os
import sys
import json
import logging
import importlib
import inspect
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

class PluginType(Enum):
    """插件类型枚举"""
    PDF_PROCESSOR = "pdf_processor"
    IMAGE_PROCESSOR = "image_processor"
    FILE_MANAGER = "file_manager"
    DEBUG_TOOL = "debug_tool"
    MAPPING_TOOL = "mapping_tool"
    UI_COMPONENT = "ui_component"

@dataclass
class PluginInfo:
    """插件信息"""
    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str]
    enabled: bool = True

class PluginInterface(ABC):
    """插件基础接口"""
    
    def __init__(self, core_services):
        self.core_services = core_services
        self.logger = core_services.get_logger(self.__class__.__name__)
    
    @abstractmethod
    def get_plugin_info(self) -> PluginInfo:
        """获取插件信息"""
        pass
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化插件"""
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理插件资源"""
        pass
    
    def get_menu_items(self) -> List[Dict[str, Any]]:
        """获取菜单项（可选）"""
        return []
    
    def get_toolbar_items(self) -> List[Dict[str, Any]]:
        """获取工具栏项（可选）"""
        return []

class CoreServices:
    """核心服务类"""
    
    def __init__(self):
        self.config = {}
        self.loggers = {}
        self.event_handlers = {}
        self.ui_framework = None
        
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志记录器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            logger.setLevel(logging.INFO)
            if not logger.handlers:
                handler = logging.StreamHandler()
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                handler.setFormatter(formatter)
                logger.addHandler(handler)
            self.loggers[name] = logger
        return self.loggers[name]
    
    def get_config(self, key: str, default=None):
        """获取配置"""
        return self.config.get(key, default)
    
    def set_config(self, key: str, value: Any):
        """设置配置"""
        self.config[key] = value
    
    def emit_event(self, event_name: str, data: Any = None):
        """发送事件"""
        if event_name in self.event_handlers:
            for handler in self.event_handlers[event_name]:
                try:
                    handler(data)
                except Exception as e:
                    self.get_logger("CoreServices").error(f"事件处理失败: {e}")
    
    def register_event_handler(self, event_name: str, handler: Callable):
        """注册事件处理器"""
        if event_name not in self.event_handlers:
            self.event_handlers[event_name] = []
        self.event_handlers[event_name].append(handler)

class PluginManager:
    """插件管理器"""
    
    def __init__(self, core_services: CoreServices):
        self.core_services = core_services
        self.logger = core_services.get_logger("PluginManager")
        self.plugins: Dict[str, PluginInterface] = {}
        self.plugin_configs: Dict[str, Dict] = {}
        self.plugins_dir = "plugins"
        
    def load_plugins(self):
        """加载所有插件"""
        if not os.path.exists(self.plugins_dir):
            os.makedirs(self.plugins_dir)
            self.logger.info(f"创建插件目录: {self.plugins_dir}")
            return
        
        # 添加插件目录到Python路径
        if self.plugins_dir not in sys.path:
            sys.path.insert(0, self.plugins_dir)
        
        # 扫描插件目录
        for item in os.listdir(self.plugins_dir):
            plugin_path = os.path.join(self.plugins_dir, item)
            
            if os.path.isdir(plugin_path):
                # 目录形式的插件
                self._load_plugin_from_directory(item, plugin_path)
            elif item.endswith('.py') and not item.startswith('__'):
                # 单文件插件
                self._load_plugin_from_file(item)
    
    def _load_plugin_from_directory(self, plugin_name: str, plugin_path: str):
        """从目录加载插件"""
        try:
            # 查找插件主文件
            main_file = os.path.join(plugin_path, '__init__.py')
            if not os.path.exists(main_file):
                main_file = os.path.join(plugin_path, f"{plugin_name}.py")
                if not os.path.exists(main_file):
                    self.logger.warning(f"插件 {plugin_name} 缺少主文件")
                    return
            
            # 导入插件模块
            spec = importlib.util.spec_from_file_location(plugin_name, main_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            self._register_plugin_from_module(plugin_name, module)
            
        except Exception as e:
            self.logger.error(f"加载插件 {plugin_name} 失败: {e}")
    
    def _load_plugin_from_file(self, filename: str):
        """从文件加载插件"""
        try:
            plugin_name = filename[:-3]  # 去掉.py扩展名
            module = importlib.import_module(plugin_name)
            self._register_plugin_from_module(plugin_name, module)
            
        except Exception as e:
            self.logger.error(f"加载插件文件 {filename} 失败: {e}")
    
    def _register_plugin_from_module(self, plugin_name: str, module):
        """从模块注册插件"""
        # 查找插件类
        plugin_classes = []
        for name, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, PluginInterface) and 
                obj != PluginInterface):
                plugin_classes.append(obj)
        
        if not plugin_classes:
            self.logger.warning(f"插件 {plugin_name} 中未找到插件类")
            return
        
        # 实例化插件
        for plugin_class in plugin_classes:
            try:
                plugin_instance = plugin_class(self.core_services)
                plugin_info = plugin_instance.get_plugin_info()
                
                # 检查依赖
                if not self._check_dependencies(plugin_info.dependencies):
                    self.logger.error(f"插件 {plugin_info.name} 依赖检查失败")
                    continue
                
                # 初始化插件
                if plugin_instance.initialize():
                    self.plugins[plugin_info.name] = plugin_instance
                    self.logger.info(f"插件 {plugin_info.name} 加载成功")
                else:
                    self.logger.error(f"插件 {plugin_info.name} 初始化失败")
                    
            except Exception as e:
                self.logger.error(f"实例化插件类 {plugin_class.__name__} 失败: {e}")
    
    def _check_dependencies(self, dependencies: List[str]) -> bool:
        """检查插件依赖"""
        for dep in dependencies:
            try:
                importlib.import_module(dep)
            except ImportError:
                self.logger.error(f"缺少依赖: {dep}")
                return False
        return True
    
    def get_plugin(self, name: str) -> Optional[PluginInterface]:
        """获取插件实例"""
        return self.plugins.get(name)
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[PluginInterface]:
        """根据类型获取插件"""
        result = []
        for plugin in self.plugins.values():
            if plugin.get_plugin_info().plugin_type == plugin_type:
                result.append(plugin)
        return result
    
    def unload_plugin(self, name: str):
        """卸载插件"""
        if name in self.plugins:
            try:
                self.plugins[name].cleanup()
                del self.plugins[name]
                self.logger.info(f"插件 {name} 已卸载")
            except Exception as e:
                self.logger.error(f"卸载插件 {name} 失败: {e}")
    
    def reload_plugin(self, name: str):
        """重新加载插件"""
        self.unload_plugin(name)
        self.load_plugins()  # 重新扫描加载
    
    def get_all_plugins(self) -> Dict[str, PluginInterface]:
        """获取所有插件"""
        return self.plugins.copy()
    
    def cleanup_all(self):
        """清理所有插件"""
        for name in list(self.plugins.keys()):
            self.unload_plugin(name)

# 使用示例
if __name__ == "__main__":
    # 创建核心服务
    core_services = CoreServices()
    
    # 创建插件管理器
    plugin_manager = PluginManager(core_services)
    
    # 加载插件
    plugin_manager.load_plugins()
    
    # 获取所有插件
    plugins = plugin_manager.get_all_plugins()
    print(f"已加载 {len(plugins)} 个插件:")
    for name, plugin in plugins.items():
        info = plugin.get_plugin_info()
        print(f"  - {info.name} v{info.version}: {info.description}")
