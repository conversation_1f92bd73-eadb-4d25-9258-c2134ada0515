import tkinter as tk
from tkinter import messagebox

def test_shortcut():
    """测试快捷键功能"""
    print("快捷键被触发了！")
    messagebox.showinfo("测试", "Ctrl+H 快捷键工作正常！")

def create_test_window():
    """创建测试窗口"""
    root = tk.Tk()
    root.title("快捷键测试")
    root.geometry("400x200")
    
    # 添加标签
    label = tk.Label(root, text="按 Ctrl+H 测试快捷键", font=("Arial", 14))
    label.pack(pady=50)
    
    # 添加按钮用于手动测试
    button = tk.Button(root, text="手动测试", command=test_shortcut, font=("Arial", 12))
    button.pack(pady=20)
    
    # 绑定快捷键
    def on_shortcut(event):
        print(f"快捷键事件: {event}")
        print(f"按键: {event.keysym}")
        print(f"状态: {event.state}")
        test_shortcut()
        return "break"
    
    # 多种绑定方式
    root.bind('<Control-h>', on_shortcut)
    root.bind('<Control-H>', on_shortcut)
    root.bind('<Control-Key-h>', on_shortcut)
    root.bind('<Control-Key-H>', on_shortcut)
    
    # 确保窗口可以接收键盘事件
    root.focus_set()
    
    print("快捷键绑定完成，请按 Ctrl+H 测试")
    root.mainloop()

if __name__ == "__main__":
    create_test_window()
