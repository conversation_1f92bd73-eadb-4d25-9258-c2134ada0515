import tkinter as tk
import sys
import ctypes

def show_console():
    """显示控制台窗口"""
    try:
        if sys.platform == "win32":
            kernel32 = ctypes.windll.kernel32
            user32 = ctypes.windll.user32
            
            # 分配控制台（如果还没有的话）
            kernel32.AllocConsole()
            
            # 获取控制台窗口句柄
            console_window = kernel32.GetConsoleWindow()
            
            if console_window:
                # 显示控制台窗口
                user32.ShowWindow(console_window, 1)  # SW_SHOWNORMAL = 1
                user32.SetForegroundWindow(console_window)
                print("=" * 50)
                print("调试终端已启用")
                print("=" * 50)
                print("这是调试信息窗口")
                print("按 Ctrl+H 可以隐藏此窗口")
                print("=" * 50)
                return True
            else:
                print("无法获取控制台窗口句柄")
                return False
        else:
            print("非Windows系统，无法显示控制台")
            return False
    except Exception as e:
        print(f"显示控制台失败: {e}")
        return False

def hide_console():
    """隐藏控制台窗口"""
    try:
        if sys.platform == "win32":
            kernel32 = ctypes.windll.kernel32
            user32 = ctypes.windll.user32
            
            # 获取控制台窗口句柄
            console_window = kernel32.GetConsoleWindow()
            
            if console_window:
                print("调试终端即将隐藏...")
                # 隐藏控制台窗口
                user32.ShowWindow(console_window, 0)  # SW_HIDE = 0
                return True
            else:
                print("无法获取控制台窗口句柄")
                return False
        else:
            print("非Windows系统，无法隐藏控制台")
            return False
    except Exception as e:
        print(f"隐藏控制台失败: {e}")
        return False

def create_test_window():
    """创建测试窗口"""
    root = tk.Tk()
    root.title("终端显示/隐藏测试")
    root.geometry("400x300")
    
    # 状态变量
    console_visible = [False]
    
    # 添加标签
    status_label = tk.Label(root, text="终端状态: 隐藏", font=("Arial", 14))
    status_label.pack(pady=20)
    
    instruction_label = tk.Label(root, text="按 Ctrl+H 切换终端显示/隐藏", font=("Arial", 12))
    instruction_label.pack(pady=10)
    
    # 添加按钮用于手动测试
    def toggle_console():
        if console_visible[0]:
            # 当前显示，需要隐藏
            if hide_console():
                console_visible[0] = False
                status_label.config(text="终端状态: 隐藏")
                print("终端已隐藏")
        else:
            # 当前隐藏，需要显示
            if show_console():
                console_visible[0] = True
                status_label.config(text="终端状态: 显示")
    
    button = tk.Button(root, text="手动切换终端", command=toggle_console, font=("Arial", 12))
    button.pack(pady=20)
    
    # 绑定快捷键
    def on_shortcut(event):
        print(f"快捷键被触发: {event.keysym}")
        toggle_console()
        return "break"
    
    # 多种绑定方式
    root.bind('<Control-h>', on_shortcut)
    root.bind('<Control-H>', on_shortcut)
    root.bind('<Control-Key-h>', on_shortcut)
    root.bind('<Control-Key-H>', on_shortcut)
    
    # 确保窗口可以接收键盘事件
    root.focus_set()
    
    # 添加键盘事件监听用于调试
    def on_key_press(event):
        if event.state & 0x4:  # Ctrl键被按下
            print(f"检测到Ctrl组合键: Ctrl+{event.keysym}")
    
    root.bind('<KeyPress>', on_key_press)
    
    print("快捷键绑定完成")
    print("请按 Ctrl+H 测试终端显示/隐藏功能")
    print("或点击按钮手动测试")
    
    root.mainloop()

if __name__ == "__main__":
    create_test_window()
